#!/usr/bin/env python3
"""
Check users on biometric device
"""

from app import app
from zk_biometric import ZKBiometricDevice

def check_device_users():
    with app.app_context():
        device = ZKBiometricDevice('192.168.1.201')
        if device.connect():
            users = device.get_users()
            print(f'📋 Found {len(users)} users on device')
            print('First 10 users:')
            for i, user in enumerate(users[:10]):
                print(f'  {i+1}. ID: {user["user_id"]}, Name: {user["name"]}')
            device.disconnect()
            
            # Test with an actual user ID
            if users:
                test_user = users[0]
                print(f'\n🧪 Testing with actual user: {test_user["user_id"]}')
                return test_user["user_id"]
        else:
            print('❌ Cannot connect to device')
            return None

if __name__ == "__main__":
    check_device_users()
