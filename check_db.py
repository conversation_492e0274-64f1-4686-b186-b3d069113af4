#!/usr/bin/env python3
"""
Quick database check script
"""

from app import app
from database import get_db

def check_database():
    with app.app_context():
        db = get_db()
        
        print("Database Tables:")
        tables = db.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
        for table in tables:
            print(f"  - {table[0]}")
        
        print("\nStaff table structure:")
        columns = db.execute("PRAGMA table_info(staff)").fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        print("\nSample data counts:")
        for table_name in ['schools', 'staff', 'attendance', 'biometric_verifications']:
            try:
                count = db.execute(f"SELECT COUNT(*) FROM {table_name}").fetchone()[0]
                print(f"  {table_name}: {count} records")
            except Exception as e:
                print(f"  {table_name}: Error - {e}")

if __name__ == "__main__":
    check_database()
